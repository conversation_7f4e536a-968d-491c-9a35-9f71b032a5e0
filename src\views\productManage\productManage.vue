<!-- 产品管理 -->
<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'ment_product_list:btn_product_add'"
          >新增</pl-button
        >
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, tableColumns)"
        >
          <template #remark="{ scope }">
            <div class="text-ellipsis" :title="scope.row.remark">
              {{ scope.row.remark }}
            </div>
          </template>
          <template #status="{ scope }">
            <span class="success-color" v-if="scope.row.status == 1">启用</span>
            <span class="error-color" v-if="scope.row.status == 0">禁用</span>
          </template>
          <template #operation="{ scope }">
            <pl-button
              link
              @click="handleDetail(scope.row)"
              v-has="'ment_product_list:btn_product_detail'"
              >查看</pl-button
            >
            <pl-button
              type="primary"
              link
              @click="handleEdit(scope.row)"
              v-has="'ment_product_list:btn_product_edit'"
              >编辑</pl-button
            >
            <pl-button
              type="danger"
              link
              @click="handleDelete(scope, 'productId')"
              v-has="'ment_product_list:btn_product_del'"
              >删除</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit"
      >
      </drawerFrom>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { getDictionaryData } from "@/api/dict";
const product_unit_type = ref([]); // 产品单位类型
onMounted(() => {
  /**
   * 产品单位类型
   */
  getDictionaryData({
    dictTypeCode: "product_unit_type",
  }).then((res) => {
    product_unit_type.value = res;
  });
});

// 加载状态控制
const tabLoading = ref(false);
// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  current,
  fromData,
  handleDetail,
  handleAdd,
  handleEdit,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
  handleDrawerSubmit,
} = useTable({
  list: "/contract/product/pageProduct",
  add: "/contract/product/saveProduct",
  edit: "/contract/product/updateProduct",
  delete: "/contract/product/deleteProduct/",
  del: {
    message: "确定删除该产品吗？",
  },
  queryForm,
});
// 查询表单的列配置
const formColumns = ref([
  {
    label: "产品名称",
    prop: "productName",
    type: "input",
    placeholder: "请输入名称",
  },
  {
    label: "单位",
    prop: "unit",
    type: "select",
    options: product_unit_type,
  },
  {
    label: "含税单价",
    prop: "unitPrice",
    type: "input",
  },
  {
    label: "状态",
    prop: "status",
    type: "select",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "禁用",
        value: 0,
      },
    ],
  },
]);

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "产品名称",
    prop: "productName",
    type: "input",
    placeholder: "请输入名称",
    rules: [
      {
        required: true,
        message: "请输入产品名称",
        trigger: "blur",
      },
    ],
  },
  {
    label: "单位",
    prop: "unit",
    type: "select",
    options: product_unit_type,
    rules: [
      {
        required: true,
        message: "请选择单位",
        trigger: "blur",
      },
    ],
  },
  {
    label: "含税单价",
    prop: "unitPrice",
    type: "number",
    rules: [
      {
        required: true,
        message: "请输入含税单价",
        trigger: "blur",
      },
    ],
    unit: "元",
  },

  {
    label: "规格",
    type: "input",
    prop: "specifications",
  },
  {
    label: "状态",
    type: "radio",
    prop: "status",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "禁用",
        value: 0,
      },
    ],
  },
  {
    label: "说明",
    type: "textarea",
    prop: "remark",
    placeholder: "请输入说明",
  },
]);

// 表格列配置
const tableColumns = ref([
  { label: "序号", type: "index", width: 80 },
  { label: "产品名称", prop: "productName", sortable: "custom", minWidth: 120 },
  {
    label: "单位",
    prop: "unitName",
    sortable: "custom",
    sortProp: "unit",
    minWidth: 120,
  },
  {
    label: "含税单价（元）",
    prop: "unitPrice",
    sortable: "custom",
    minWidth: 160,
  },
  { label: "说明", template: "remark", sortable: "custom", minWidth: 120 },
  {
    label: "状态",
    prop: "status",
    template: "status",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
