<template>
  <!-- 卡片组件开始 -->
  <pl-card class="unified-ticket-sales">
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        formType="1"
        @confirm="search"
        @cancel="cancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->
      <pl-drag-range :initialLeftWidth="1000">
        <template #left>
          <div class="left-content-ticket">
            <!-- 表格展示区域开始 -->
            <div class="card-table">
              <div v-show="showTable">
                <pl-table
                  :columns="tableColumns"
                  :data="tableData"
                  class="table"
                  v-loading="tabLoading"
                  border
                  @sort-change="handleSort"
                  @cell-click="cellClick"
                >
                  <template #operation="{ scope }">
                    <pl-button type="primary" @click="handleEdit(scope.row)"
                      >编辑</pl-button
                    >
                    <pl-button
                      type="danger"
                      @click="handleDelete(scope.row, '设置成需要删除的id名字')"
                      >删除</pl-button
                    >
                  </template>
                </pl-table>
              </div>
              <div v-show="!showTable">
                <div class="seat-empty">
                  <div class="seat-empty-text">🔍请搜索售票信息</div>
                </div>
              </div>
            </div>
            <!-- 表格展示区域结束 -->

            <!-- 分页组件开始 -->
            <pl-pagination
              v-show="showTable"
              :total="dataTotal"
              @size-change="sizeChange"
              @current-change="currentChange"
            ></pl-pagination>
            <!-- 分页组件结束 -->
          </div>
        </template>
        <template #right>
          <div class="seat">
            <div
              class="auto-scheduling-tip"
              v-if="curIsAuto && curIsSupportSell"
            >
              <div class="tip-box">
                <div class="tip-icon">🚌</div>
                <div class="tip-content">
                  <div class="tip-title">滚动发车</div>
                  <div class="tip-desc">座位将随机分配</div>
                </div>
              </div>
            </div>
            <seatSelect
              v-if="showSeat"
              isSale
              :dropValueData="[]"
              :seatInfoSetList="seatData"
              @clickSeat="seatSelectChange"
            ></seatSelect>
            <div v-else>
              <div class="seat-empty">
                <div class="seat-empty-text">🎫请选择左侧班次</div>
              </div>
            </div>
          </div>
        </template>
      </pl-drag-range>

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
      >
      </drawerFrom>
    </div>
    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <pl-button class="btn" type="primary" @click="handleSaveData"
        >确定</pl-button
      >
    </div>

    <!-- 确认购票弹窗 -->
    <pl-dialog
      v-model="dialogVisible"
      title="乘车人信息"
      append-to-body
      align-center
      showCancel
      :loading="affirmLoad"
      @confirm="handleConfirm"
      @cancel="dialogVisible = false"
    >
      <template #content>
        <!-- 票种类型 -->
        <div class="info-item">
          <div class="info-item-label">票种类型:</div>
          <pl-select
            v-model="affirmTicket.passengers[0].ticketType"
            :options="ticketTypeList"
            @change="ticketTypeChange"
          ></pl-select>
        </div>
        <!-- 乘车人 -->
        <div class="info-item">
          <div class="info-item-label">乘车人:</div>
          <pl-input v-model="affirmTicket.passengers[0].passName"></pl-input>
        </div>
        <!-- 联系电话 -->
        <div class="info-item">
          <div class="info-item-label">联系电话:</div>
          <pl-input
            type="number"
            v-model="affirmTicket.passengers[0].passPhone"
            placeholder="请输入手机号码"
          ></pl-input>
        </div>
        <!-- 身份证号 -->
        <div class="info-item">
          <div class="info-item-label">身份证号:</div>
          <pl-input
            v-model="affirmTicket.passengers[0].passId"
            placeholder="请输入身份证号码"
          ></pl-input>
        </div>
        <!-- 付款方式 -->
        <div class="info-item">
          <div class="info-item-label">付款方式:</div>
          <pl-select
            v-model="affirmTicket.paymentWay"
            :options="payTypeList"
            @change="priceTypeChange"
          ></pl-select>
        </div>
        <!-- 付款金额 -->
        <div class="info-item">
          <div class="info-item-label">
            优惠{{ ticketRule.discountAmount >= 0 ? "/上涨" : "/下调" }}金额:
          </div>
          <pl-input disabled v-model="ticketRule.discountAmount"></pl-input>
        </div>
        <!-- 计价类型 -->
        <div class="info-item">
          <div class="info-item-label">计价类型:</div>
          <pl-input
            disabled
            v-model="
              affirmTicket.passengers[0].ticketPricingRuleInfoList
                .pricingTypeFloatPriceMapVO
            "
          ></pl-input>
        </div>
        <!-- 附加费 -->
        <div class="info-item">
          <div class="info-item-label">附加费:</div>
          <pl-input disabled v-model="ticketRule.surchargeAmount">
            <template #suffix>
              <span>#</span>
            </template>
          </pl-input>
        </div>
        <!-- 金额 -->
        <div class="info-item">
          <div class="info-item-label">支付金额:</div>
          <pl-input disabled v-model="ticketRule.paymentAmount"></pl-input>
        </div>
      </template>
    </pl-dialog>
  </pl-card>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { getDictionaryData } from "@/api/dict";
import seatSelect from "@/components/seat-select/seat-select.vue";
import { tableColumns } from "./config";
import {
  getSiteList,
  getVehicleModelList,
  getTicketSeat,
  tickPriceCal,
  payTicketOrder,
  confirmPayTicketOrder,
  getBusinessDetail,
} from "@/api";
import { plMessage } from "pls-common";
import { getCookie } from "@/utils/cookie";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

onMounted(() => {
  getSite();
  getVehicleModel();
  getDict();
});

// 查询表单的数据
const fixedField = ref({
  stepTime: ["00:00:00", "23:59:59"],
  stepDate: getCurrentDate(),
});

const queryForm = ref({
  stepTime: ["00:00:00", "23:59:59"],
  stepDate: getCurrentDate(),
});

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  drawerVisible,
  tabLoading,
  drawerTitle,
  formDisabled,
  tableData,
  handleEdit,
  handleDelete,
  handleSort,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/ticket/price/pageTicketPrice",
  noLoad: true,
  queryForm,
  ...fixedField.value,
});
const emit = defineEmits(["close"]);
const handleClose = () => {
  emit("close");
  // 重置搜索/表单/选座状态
  reset();
};

// 获取站点列表
const siteList = ref([]);
const getSite = async () => {
  const { data: res } = await getSiteList({ stopMode: "DZ" });
  siteList.value = res;
};
// 获取车型列表
const vehicleModelList = ref([]);
const getVehicleModel = async () => {
  const { data: res } = await getVehicleModelList({});
  vehicleModelList.value = res;
};
// 获取字典数据
const ticketTypeList = ref([]); // 票据类型
const ticketFromList = ref([]); // 票据来源
const payTypeList = ref([]); // 支付方式
const priceTypeList = ref([]); // 计价类型
const getDict = async () => {
  const res = await getDictionaryData({
    dictTypeCode: "ticket_type",
  });
  ticketTypeList.value = res;
  const res2 = await getDictionaryData({
    dictTypeCode: "order_platform",
  });
  ticketFromList.value = res2;
  const res3 = await getDictionaryData({
    dictTypeCode: "pricing_type",
  });
  priceTypeList.value = res3;
  const res4 = await getDictionaryData({
    dictTypeCode: "payment_way",
  });
  payTypeList.value = res4;
  payTypeList.value.forEach((item) => {
    if (item.value !== "XJ") {
      item.disabled = true;
    }
  });
};

// 点击搜索
const showTable = ref(false);
// 重置
const search = () => {
  if (!queryForm.value.stepDate) {
    plMessage("请选择乘车日期", "warning");
    return;
  }
  if (!queryForm.value.stepTime || !queryForm.value.stepTime.length) {
    plMessage("请选择乘车时间", "warning");
    return;
  }
  if (!queryForm.value.startStop) {
    plMessage("请选择乘车起点", "warning");
    return;
  }
  if (!queryForm.value.endStop) {
    plMessage("请选择乘车终点", "warning");
    return;
  }

  // 清除右侧座位选择
  showSeat.value = false;
  curIsAuto.value = false;
  curIsSupportSell.value = true;

  // 拼接搜索参数
  queryForm.value.startDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[0];
  queryForm.value.endDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[1];
  queryForm.value.startStopId = queryForm.value.startStop;
  queryForm.value.endStopId = queryForm.value.endStop;
  queryForm.value["orderPlatform"] = "SPCK";
  handleSearch();
  showTable.value = true;
};

// 点击单元格
const curIsAuto = ref(false); // 是否滚动发车
const curIsSupportSell = ref(true); // 是否支持售票
const showSeat = ref(false);
const curTicketPriceId = ref("");
const seatData = ref([]);
const cellClick = async ({
  schedulingItemId,
  ticketPriceId,
  isSupportSell,
  isAutoScheduling,
}) => {
  // 滚动发车：1  固定发车：0
  curIsAuto.value = isAutoScheduling == 1 ? true : false;

  if (!isSupportSell) {
    plMessage("该班次不支持售票", "warning");
    curIsSupportSell.value = false;
    return;
  } else {
    curIsSupportSell.value = true;
  }

  if (curIsAuto.value == 1) {
    affirmTicket.value.passengers[0].seatNum = "xx";
    curTicketPriceId.value = ticketPriceId;
  }

  showSeat.value = true;
  curTicketPriceId.value = ticketPriceId;
  const { data: res } = await getTicketSeat({
    ticketPriceId,
    schedulingItemId,
  });
  seatData.value = res.seatInfo ? JSON.parse(res.seatInfo) : [];
};

const dialogVisible = ref(false); // 乘车人信息弹窗
// 确定乘车人表单
const affirmTicket = ref({
  businessType: "", // 业务类型
  discountAmount: "", // 总优惠金额
  orderAmount: "", // 总订单金额
  isRebook: false, // 是否改签
  orderPlatform: "", // 订单来源
  orgId: "", // 组织id
  passengers: [
    {
      passId: "", // 身份证号
      passName: "", // 乘车人
      passPhone: "", // 联系电话
      seatNum: "", // 座位号
      ticketType: "", // 票种类型
      ticketPricingRuleInfoList: {
        originalPrice: "", // 原价
        price: "", // 票价
        pricingTypeFloatPriceMap: "", //  计价类型浮动金额列表
        pricingTypeFloatPriceMapVO: "", //  计价类型浮动金额列表
        surcharge: "", // 附加费
      },
    },
  ],
  paymentAmount: "", // 	总支付金额
  paymentWay: "", // 付款方式
  surchargeAmount: "", // 总附加费
  ticketPriceId: "", // 	票价id
  isOversold: false, // 是否超卖，默认false
});
// 计价规则数据
const ticketRule = ref({
  discountAmount: "",
  orderAmount: "",
  paymentAmount: "",
  surchargeAmount: "",
  ticketPricingRuleInfoList: [],
});
// 票种类型选择
const curTickType = ref({});
const ticketTypeChange = async (e, item) => {
  curTickType.value = item;
  const { data: res } = await tickPriceCal({
    ticketPriceId: curTicketPriceId.value,
    ticketTypeList: [item.value],
  });

  // ? 赋值
  ticketRule.value = res;

  // 计价类型转换
  const pricingMap = res.ticketPricingRuleInfoList[0].pricingTypeFloatPriceMap;
  const convertedPricing = Object.entries(pricingMap)
    .map(([key]) => {
      const matchedType = priceTypeList.value.find(
        (type) => type.value === key
      );
      return matchedType ? `${matchedType.label}` : `${key}`;
    })
    .join(",");
  // 赋值转换后的计价类型字符串
  affirmTicket.value.passengers[0].ticketPricingRuleInfoList.pricingTypeFloatPriceMapVO =
    convertedPricing;
};

// 确认选择座位
const handleSaveData = () => {
  console.log("save", curTicketPriceId.value);
  // if (curIsAuto.value == 1) {
  //   plMessage("请选择班次", "warning");
  //   return;
  // }
  if (!curTicketPriceId.value) {
    plMessage("请选择班次", "warning");
    return;
  }
  if (!curSiteInfo.value.num && curIsAuto.value == 0) {
    plMessage("请选择座位", "warning");
    return;
  }
  dialogVisible.value = true;
};
// 座次选中
const curSiteInfo = ref({});
const seatSelectChange = (data) => {
  curSiteInfo.value = data;
};
// 付款方式change
const priceTypeChange = (e, item) => {
  console.log(e, item);
};

// 确认乘车人
const affirmLoad = ref(false); // 确认按钮load
const handleConfirm = async () => {
  // 基础信息校验
  if (!curTickType.value.label) {
    return plMessage("请选择票种类型", "warning");
  }
  if (!affirmTicket.value.passengers[0].passName) {
    return plMessage("请输入乘车人", "warning");
  }

  // 手机号校验
  const phoneReg = /^1[3-9]\d{9}$/;
  const phone = affirmTicket.value.passengers[0].passPhone;
  if (!phone) {
    return plMessage("请输入联系电话", "warning");
  }
  if (!phoneReg.test(phone)) {
    return plMessage("请输入正确的手机号码", "warning");
  }
  // 身份证号校验
  const idCardReg =
    /(^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)?$)/;
  const idCard = affirmTicket.value.passengers[0].passId;
  if (!idCard) {
    return plMessage("请输入身份证号", "warning");
  }
  if (!idCardReg.test(idCard)) {
    return plMessage("请输入正确的身份证号码", "warning");
  }
  if (!affirmTicket.value.paymentWay) {
    return plMessage("请选择付款方式", "warning");
  }

  affirmLoad.value = true;
  try {
    const params = {
      ...affirmTicket.value,
    };
    // 票种类型
    params.orgId = JSON.parse(getCookie("userInfo")).orgId;
    params.paymentAmount = ticketRule.value.paymentAmount;
    params.paymentWay = affirmTicket.value.paymentWay;
    params.orderPlatform = "SPCK";
    params.discountAmount = ticketRule.value.discountAmount;
    params.orderAmount = ticketRule.value.orderAmount;
    params.passengers[0].seatNum =
      curIsAuto.value == 1 ? "xx" : curSiteInfo.value.num;

    params.surchargeAmount = ticketRule.value.surchargeAmount;
    params.passengers[0].passId = affirmTicket.value.passengers[0].passId;
    params.passengers[0]["ticketPricingRuleInfoVO"] =
      ticketRule.value.ticketPricingRuleInfoList[0];
    params.ticketPriceId = curTicketPriceId.value;
    params.isRebook = false;
    params.isOversold = false;

    // 获取业务类型编码
    const { data: typeRes } = await getBusinessDetail({
      businessCode: "DZZX",
    });
    params.businessType = typeRes.businessType;
    // console.log("affirm params", params);
    const res = await payTicketOrder(params);
    if (res.code == 200) {
      const orderRes = await confirmPayTicketOrder({
        orderNo: res.data,
        isOversold: false,
        paymentWay: affirmTicket.value.paymentWay,
        businessType: typeRes.businessType,
        orderPlatform: "SPCK",
        orgId: JSON.parse(getCookie("userInfo")).orgId,
      });
      if (orderRes.code == 200) {
        affirmLoad.value = false;
        plMessage(orderRes.message, "success");
        dialogVisible.value = false;
        handleClose();
      }
    }
  } catch (error) {
    console.log("err", error);
  } finally {
    affirmLoad.value = false;
  }
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "乘车日期",
    prop: "stepDate",
    type: "date",
    format: "YYYY-MM-DD",
    placeholder: "请输入乘车日期",
  },
  {
    label: "乘车时间",
    prop: "stepTime",
    type: "time-picker",
    format: "HH:mm:ss",
  },
  {
    label: "乘车起点",
    prop: "startStop",
    type: "select",
    filterable: true,
    labelKey: "stopName",
    valueKey: "stopId",
    options: siteList,
    clearable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "乘车终点",
    prop: "endStop",
    type: "select",
    filterable: true,
    labelKey: "stopName",
    valueKey: "stopId",
    options: siteList,
  },
  {
    label: "车型",
    prop: "vehicleModelId",
    type: "select",
    options: vehicleModelList,
    labelKey: "vehicleModelName",
    valueKey: "vehicleModelId",
  },
]);
// 默认乘车日期修改
function getCurrentDate() {
  const currentDate = new Date().toISOString().split("T")[0];
  return currentDate;
}

// 弹窗表单数据 可设置默认数据 如：fromData.value = { status: "1" };
const fromData = ref({});

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "名称",
    prop: "name",
    type: "input",
    placeholder: "请输入名称",
  },
]);

// 关闭弹窗清除状态
const reset = () => {
  queryForm.value = {};
  tableData.value = [];
  showTable.value = false;
  showSeat.value = false;
  // 重置 affirmTicket 为初始状态
  affirmTicket.value = {
    businessType: "",
    discountAmount: "",
    orderAmount: "",
    orderPlatform: "",
    orgId: "",
    passengers: [
      {
        passId: "",
        passName: "",
        passPhone: "",
        seatNum: "",
        ticketType: "",
        ticketPricingRuleInfoList: {
          originalPrice: "",
          price: "",
          pricingTypeFloatPriceMap: "",
          pricingTypeFloatPriceMapVO: "",
          surcharge: "",
        },
      },
    ],
    paymentAmount: "",
    paymentWay: "",
    surchargeAmount: "",
    ticketPriceId: "",
  };
  // 重置 curTickType
  curTickType.value = {};
  // 重置 curSiteInfo
  curSiteInfo.value = {};
  ticketRule.value = {};
  // 重置 seatData
  seatData.value = [];
  // 重置所选班次id
  curTicketPriceId.value = "";
};
// 重置按钮点击
const cancel = () => {
  tableData.value = [];
  showTable.value = false;
  showSeat.value = false;
  // 重置 affirmTicket 为初始状态
  affirmTicket.value = {
    businessType: "",
    discountAmount: "",
    orderAmount: "",
    orderPlatform: "",
    orgId: "",
    passengers: [
      {
        passId: "",
        passName: "",
        passPhone: "",
        seatNum: "",
        ticketType: "",
        ticketPricingRuleInfoList: {
          originalPrice: "",
          price: "",
          pricingTypeFloatPriceMap: "",
          pricingTypeFloatPriceMapVO: "",
          surcharge: "",
        },
      },
    ],
    paymentAmount: "",
    paymentWay: "",
    surchargeAmount: "",
    ticketPriceId: "",
  };
  // 重置 curTickType
  curTickType.value = {};
  // 重置 curSiteInfo
  curSiteInfo.value = {};
  ticketRule.value = {};
  // 重置 seatData
  seatData.value = [];
  // 重置所选班次id
  curTicketPriceId.value = "";

  // 清除右侧座位选择
  showSeat.value = false;
  curIsAuto.value = false;
  curIsSupportSell.value = true;

  // 拼接搜索参数
  queryForm.value.startDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[0];
  queryForm.value.endDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[1];
  queryForm.value.startStopId = queryForm.value.startStop;
  queryForm.value.endStopId = queryForm.value.endStop;
  queryForm.value["orderPlatform"] = "SPCK";
  queryForm.value["startStop"] = "";
  queryForm.value["startStopId"] = "";
  queryForm.value["endStop"] = "";
  queryForm.value["endStopId"] = "";
  queryForm.value["vehicleModelId"] = "";
  handleSearch();
};
const resetDialog = () => {
  affirmTicket.value.passengers[0].ticketType = "";
  affirmTicket.value.passengers[0].passName = "";
  affirmTicket.value.passengers[0].passPhone = "";
  affirmTicket.value.passengers[0].passId = "";
  affirmTicket.value.passengers[0].ticketPricingRuleInfoList.pricingTypeFloatPriceMapVO =
    "";
  affirmTicket.value.paymentWay = "";
  affirmTicket.value.paymentAmount = "";
  affirmTicket.value.surchargeAmount = "";
  ticketRule.value.discountAmount = "";
  ticketRule.value.surchargeAmount = "";
  ticketRule.value.paymentAmount = "";
};

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      queryForm.value = {
        stepTime: ["00:00:00", "23:59:59"],
        stepDate: getCurrentDate(),
      };
      fixedField.value = {
        stepTime: ["00:00:00", "23:59:59"],
        stepDate: getCurrentDate(),
      };
    }
  }
);

watch(
  () => dialogVisible.value,
  (newVal) => {
    if (!newVal) {
      console.log("关闭dialog");
      resetDialog();
    }
  }
);
</script>

<style lang="scss" scoped>
.button-box {
  z-index: 99;
}
.left-content-ticket {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.unified-ticket-sales {
  padding-bottom: 60px !important;
}

.seat-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  font-size: 14px;
  color: #999;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .info-item-label {
    white-space: nowrap;
    min-width: 100px;
  }
}

.seat {
  width: 100%;
  height: 100%;
  position: relative;
}
.auto-scheduling-tip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  .tip-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 16px;
    max-width: 320px;
    text-align: center;
  }
  .tip-icon {
    font-size: 32px;
    flex-shrink: 0;
  }

  .tip-content {
    flex: 1;

    .tip-title {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .tip-desc {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
    }
  }
}

.placeholder {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  border-radius: 12px;
  top: 0;
  left: 0;
  z-index: 9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #161616;
  text-align: center;
  padding: 10px;
}
</style>
