<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
        <template #questionType>
          <pl-dict-select
            dictCode="question_type"
            v-model="queryForm.questionType"
            multiple
          ></pl-dict-select>
        </template>
        <template #questionSource>
          <pl-dict-select
            dictCode="question_source"
            v-model="queryForm.questionSource"
            multiple
          ></pl-dict-select>
        </template>
      </pl-form>
      <!-- 新增租户按钮 -->
      <div>
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'menu_feedback_list:btn_feedback_add'"
        >
          新增
        </pl-button>
      </div>
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
        >
          <template #operation="{ scope }">
            <pl-button
              link
              @click="handleDetails(scope.row)"
              v-has="'menu_feedback_list:btn_feedback_view'"
              >查看</pl-button
            >
            <pl-button
              link
              type="primary"
              @click="handleHandle(scope.row)"
              v-if="scope.row.processStatus == 2"
              v-has="'menu_feedback_list:btn_feedback_open'"
              >再次打开</pl-button
            >
            <pl-button
              link
              type="primary"
              v-if="
                scope.row.processStatus == 0 || scope.row.processStatus == 1
              "
              v-has="'menu_feedback_list:btn_feedback_deal'"
              @click="handleHandle(scope.row)"
              >去处理</pl-button
            >
            <pl-button
              link
              type="primary"
              v-if="scope.row.processStatus == 1"
              v-has="'menu_feedback_list:btn_feedback_complete'"
              @click="handleFinish(scope.row)"
              >处理完成</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 分页 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 新建问题 start -->
    <pl-drawer :title="drawerTitle" v-model="drawerVisible">
      <addProblemPopup
        @close="drawerVisible = false"
        @submit="handleSubmit"
        v-if="drawerVisible"
      ></addProblemPopup>
    </pl-drawer>
    <!-- 新建问题 end -->

    <!-- 问题详情 start -->
    <pl-drawer title="问题详情" v-model="problemDetailsDrawer">
      <problemDetails
        @close="problemDetailsDrawer = false"
        v-if="problemDetailsDrawer"
        :feedBackId="feedBackId"
      ></problemDetails>
    </pl-drawer>
    <!-- 问题详情 end -->

    <!-- 问题处理 start -->
    <pl-drawer title="问题处理" v-model="problemHandleDrawer">
      <problemHandle
        v-if="problemHandleDrawer"
        :feedBackId="feedBackId"
        :content="content"
        @refresh="problemRefresh"
      ></problemHandle>
    </pl-drawer>
    <!-- 问题处理 end -->
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import addProblemPopup from "./add-problem-popup.vue";
import problemDetails from "./problem-details.vue";
import problemHandle from "./problem-handle.vue";
import { useTable } from "@/hooks/usetTable";
import { plMessage, plMessageBox } from "pls-common";
import { saveFeedBackProcess, getAppList, getEmployeeList } from "@/api/index";
import { getCookie } from "@/utils/cookie";
const problemDetailsDrawer = ref(false);
const problemHandleDrawer = ref(false);
const feedBackId = ref("");

// 问题处理刷新
const problemRefresh = () => {
  problemHandleDrawer.value = false;
  loadData();
};
// 查看详情
const handleDetails = (row) => {
  feedBackId.value = row.feedBackId;
  problemDetailsDrawer.value = true;
};

const appList = ref([]);
getAppList().then((res) => {
  appList.value = res.data;
});

// 处理完成
const handleFinish = (row) => {
  plMessageBox
    .confirm("请确认问题是否处理完成", "操作确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(() => {
      saveFeedBackProcess({
        feedBackId: row.feedBackId,
        processBy: JSON.parse(getCookie("userInfo")).userId,
        processStatus: 2,
      }).then((res) => {
        if (res.code == 200) {
          plMessage("处理完成", "success");
          loadData();
        } else {
          plMessage(res.message, "error");
        }
      });
    })
    .catch(() => {
      console.log("取消删除");
    });
};
const content = ref("");
// 去处理
const handleHandle = async (row) => {
  feedBackId.value = row.feedBackId;
  content.value = row.content;
  problemHandleDrawer.value = true;
};

const queryForm = ref({});
const {
  drawerTitle, // 抽屉标题
  drawerVisible, // 抽屉显示状态
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  handleAdd,
  loadData, // 加载数据方法
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleCancel, // 取消/重置方法
  handleSearch, // 搜索方法
  handleDrawerSubmit, // 抽屉提交方法
  current,
} = useTable({
  queryForm,
  list: "/feedBack/pageFeedBack", // 列表接口
  edit: "/sc/travelLine/updateTravelLine", // 编辑接口
  add: "/feedBack/saveFeedBackByAdmin", // 新增接口
  del: {
    message: "确定要删除该线路数据吗?",
    type: "message",
  },
});

// 新增问题
const handleSubmit = (data) => {
  handleDrawerSubmit(data);
};
const employeeList = ref([]);
getEmployeeList().then((res) => {
  employeeList.value = res.data;
});
const formColumns = ref([
  {
    label: "反馈人电话",
    type: "input",
    prop: "phone",
  },
  {
    label: "反馈时间",
    type: "datetimerange",
    prop: "startTimeAndendTime",
    format: "YYYY-MM-DD HH:mm:ss",
    onChange: (val) => {
      queryForm.value.startTime = val[0];
      queryForm.value.endTime = val[1];
      // delete queryForm.value.startTimeAndendTime;
    },
  },
  {
    label: "处理状态",
    type: "select",
    prop: "processStatus",
    options: [
      {
        label: "未处理",
        value: 0,
      },
      {
        label: "处理中",
        value: 1,
      },
      {
        label: "已完结",
        value: 2,
      },
    ],
  },
  {
    label: "处理人",
    type: "select",
    prop: "processBy",
    options: employeeList,
    valueKey: "userId",
    labelKey: "username",
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "问题来源",
    type: "select",
    prop: "questionSource",
    template: "questionSource",
  },
  {
    label: "问题类别",
    type: "select",
    prop: "questionType",
    template: "questionType",
  },
  {
    label: "所属应用",
    type: "select",
    prop: "appCode",
    options: appList,
    valueKey: "appCode",
    labelKey: "appName",
    multiple: true,
  },
]);

const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: "60",
  },
  {
    label: "问题来源",
    prop: "questionSource",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "所属应用",
    prop: "appName",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "问题类别",
    prop: "questionType",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "问题描述",
    prop: "content",
    sortable: "custom",
    minWidth: 180,
  },
  {
    label: "最近一次处理时间",
    prop: "processTime",
    sortable: "custom",
    minWidth: 200,
  },
  {
    label: "处理人",
    prop: "processName",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "处理状态",
    prop: "processStatusStr",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "反馈时间",
    prop: "createTime",
    sortable: "custom",
    minWidth: 180,
  },
  {
    label: "操作",
    template: "operation",
    setting: true,
    fixed: "right",
    minWidth: 120,
  },
]);
</script>

<style lang="scss" scoped></style>
